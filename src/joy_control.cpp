#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <geometry_msgs/msg/twist_stamped.hpp>
#include <sensor_msgs/msg/joy.hpp>
#include <std_msgs/msg/string.hpp>
#include <chrono>

class JoyControl : public rclcpp::Node
{
public:
    /**
     * @brief: Class constructor, gets ROS parameters, and creates subscribers and publishers.
     * @return {*}
     */
    JoyControl() : Node("joy_control")
    {
        this->declare_parameter("use_stamp_vel", true);
        this->declare_parameter("AXIS_FORWARD_BACKWARD", 5);
        this->declare_parameter("AXIS_LEFT_RIGHT", 4);
        this->declare_parameter("KEY_TURN_LEFT", 3);
        this->declare_parameter("KEY_TURN_RIGHT", 1);
        this->declare_parameter("KEY_SPEED_UP", 9);

        this->declare_parameter("low_vel_x", 0.3);
        this->declare_parameter("high_vel_x", 0.5);
        this->declare_parameter("low_vel_yaw", 0.3);
        this->declare_parameter("high_vel_yaw", 0.8);
        this->declare_parameter("joy_timeout_ms", 500.0); // Joystick timeout in milliseconds

        this->get_parameter("AXIS_FORWARD_BACKWARD", AXIS_FORWARD_BACKWARD);
        this->get_parameter("AXIS_LEFT_RIGHT", AXIS_LEFT_RIGHT);
        this->get_parameter("KEY_TURN_LEFT", KEY_TURN_LEFT);
        this->get_parameter("KEY_TURN_RIGHT", KEY_TURN_RIGHT);
        this->get_parameter("KEY_SPEED_UP", KEY_SPEED_UP);

        this->get_parameter("low_vel_x", low_vel_x_);
        this->get_parameter("high_vel_x", high_vel_x_);
        this->get_parameter("low_vel_yaw", low_vel_yaw_);
        this->get_parameter("high_vel_yaw", high_vel_yaw_);
        this->get_parameter("use_stamp_vel", use_stamp_vel_);
        this->get_parameter("joy_timeout_ms", joy_timeout_ms_);

        if (use_stamp_vel_)
        {
            vel_stamp_pub_ = this->create_publisher<geometry_msgs::msg::TwistStamped>("/cmd_vel_joy", 1); // Publish chassis speed
        }
        else
        {
            vel_pub_ = this->create_publisher<geometry_msgs::msg::Twist>("/cmd_vel_joy", 1); // Publish chassis speed
        }
        
        Nstop = 0;
        stop_counter_ = 0;
        
        // Initialize timestamp
        last_joy_time_ = this->now();
        is_joy_connected_ = false;

        // Create a timer to check joystick connection status
        timeout_timer_ = this->create_wall_timer(
            std::chrono::milliseconds(100), // Check every 100ms
            std::bind(&JoyControl::checkJoyTimeout, this));

        // Subscribe to joystick messages
        joy_sub_ = create_subscription<sensor_msgs::msg::Joy>("/joy", 1, std::bind(&JoyControl::joySubCallback, this, std::placeholders::_1));
    }

private:
    int AXIS_FORWARD_BACKWARD;
    int KEY_TURN_LEFT;
    int KEY_TURN_RIGHT;
    int KEY_SPEED_UP;

    double low_vel_x_;
    double high_vel_x_;
    double low_vel_yaw_;
    double high_vel_yaw_;
    bool use_stamp_vel_;
    double joy_timeout_ms_; // Joystick timeout in milliseconds

    int Nstop;
    int stop_counter_;

    // Joystick connection status variables
    rclcpp::Time last_joy_time_; // Time of last received joystick message
    bool is_joy_connected_;      // Joystick connection status

    rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr vel_pub_;
    rclcpp::Publisher<geometry_msgs::msg::TwistStamped>::SharedPtr vel_stamp_pub_;
    rclcpp::Subscription<sensor_msgs::msg::Joy>::SharedPtr joy_sub_;
    rclcpp::TimerBase::SharedPtr timeout_timer_; // Timeout check timer

    /**
     * @brief: Converts joystick message to chassis speed.
     * @param {SharedPtr} msg Joystick message
     * @param {Twist} &cmd_vel Chassis speed
     * @return {*}
     */
    void toBaseSpeed(const sensor_msgs::msg::Joy::SharedPtr msg, geometry_msgs::msg::Twist &cmd_vel)
    {
        bool speedup = false;
        if (msg->buttons[KEY_SPEED_UP] == 1)
        {
            speedup = true; // High speed
            RCLCPP_DEBUG(this->get_logger(), "Speed up button pressed!");
        }

        if (msg->buttons[KEY_TURN_LEFT] == 1)
        {
            if (speedup)
                cmd_vel.angular.z = high_vel_yaw_;
            else
                cmd_vel.angular.z = low_vel_yaw_;
        }
        else if (msg->buttons[KEY_TURN_RIGHT] == 1)
        {
            if (speedup)
                cmd_vel.angular.z = -high_vel_yaw_;
            else
                cmd_vel.angular.z = -low_vel_yaw_;
        }

        int temp = judgeKey(msg->axes[AXIS_FORWARD_BACKWARD]);
        if (temp == 1)
        {
            if (speedup)
                cmd_vel.linear.x = high_vel_x_;
            else
                cmd_vel.linear.x = low_vel_x_;
        }
        else if (temp == -1)
        {
            if (speedup)
                cmd_vel.linear.x = -high_vel_x_;
            else
                cmd_vel.linear.x = -low_vel_x_;
        }
    }

    /**
     * @brief: Publishes the velocity message.
     * @param {Twist} twist Velocity message
     * @return {*}
     */
    void publishVelCmd(const geometry_msgs::msg::Twist &twist)
    {
        if (use_stamp_vel_)
        {
            geometry_msgs::msg::TwistStamped cmd_vel;
            cmd_vel.header.stamp = this->now(); // Add current timestamp
            cmd_vel.twist = twist;
            vel_stamp_pub_->publish(cmd_vel);
        }
        else
        {
            vel_pub_->publish(twist);
        }
    }

    /**
     * @brief: Checks for joystick timeout and sends a stop command.
     * @return {*}
     */
    void checkJoyTimeout()
    {
        auto current_time = this->now();
        auto time_diff = (current_time - last_joy_time_).seconds() * 1000.0; // Convert to milliseconds

        if (time_diff > joy_timeout_ms_)
        {
            if (is_joy_connected_)
            {
                // Joystick just disconnected, send stop command
                RCLCPP_WARN(this->get_logger(), "Joy controller disconnected! Sending zero velocity command.");

                // Send stop velocity command
                geometry_msgs::msg::Twist stop_twist; // All velocities default to 0
                publishVelCmd(stop_twist);

                is_joy_connected_ = false;
            }
            else
            {
                if (stop_counter_ < 3)
                {
                    RCLCPP_INFO(this->get_logger(), "Resending zero velocity command (%d/3).", stop_counter_ + 1);
                    geometry_msgs::msg::Twist stop_twist;
                    publishVelCmd(stop_twist);
                    stop_counter_++;
                }
            }
        }
        else
        {
            if (!is_joy_connected_)
            {
                // Joystick reconnected
                RCLCPP_INFO(this->get_logger(), "Joy controller reconnected!");
                stop_counter_ = 0; // Reset counter on reconnection
                is_joy_connected_ = true;
            }
        }
    }

    /**
     * @brief: Joystick message callback function, processes joystick input to motion info.
     * @param {SharedPtr} msg Joystick message
     * @return {*}
     */
    void joySubCallback(const sensor_msgs::msg::Joy::SharedPtr msg)
    {
        // Update the time of the last received message
        last_joy_time_ = this->now();

        // If joystick just reconnected, mark as connected
        if (!is_joy_connected_)
        {
            RCLCPP_INFO(this->get_logger(), "Joy controller reconnected!");
            is_joy_connected_ = true;
        }
        
        std_msgs::msg::String cmd;

        // Chassis movement
        geometry_msgs::msg::Twist twist;
        toBaseSpeed(msg, twist);

        // Debug output
        RCLCPP_DEBUG(this->get_logger(), "Twist: linear.x=%.3f, angular.z=%.3f",
                    twist.linear.x, twist.angular.z);

        // Check if all speeds are zero to decide whether to stop publishing
        if (twist.linear.x == 0  && twist.angular.z == 0)
        {
            if (Nstop < 5)
            {
                ++Nstop;
                publishVelCmd(twist);
                RCLCPP_DEBUG(this->get_logger(), "Publishing zero velocity, Nstop=%d", Nstop);
            }
            else
            {
                RCLCPP_DEBUG(this->get_logger(), "Stopped publishing (Nstop=%d)", Nstop);
            }
        }
        else
        {
            Nstop = 0;
            publishVelCmd(twist);
            RCLCPP_DEBUG(this->get_logger(), "Publishing non-zero velocity");
        }
    }

    /**
     * @brief: Normalizes velocity direction.
     * @param {double} &vel Velocity
     * @return {*}
     */
    int judgeKey(const double &vel)
    {
        // A small epsilon to account for float precision issues
        const double EPSILON = 0.01;
        if (vel >= EPSILON)
            return 1;
        else if (vel <= -EPSILON)
            return -1;
        else
            return 0;
    }
};

int main(int argc, char **argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<JoyControl>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}
