#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <geometry_msgs/msg/twist_stamped.hpp>
#include <sensor_msgs/msg/joy.hpp>
#include <std_msgs/msg/string.hpp>
#include <chrono>

class JoyControl : public rclcpp::Node
{
public:
    /**
     * @brief: Class constructor, gets ROS parameters, and creates subscribers and publishers.
     * @return {*}
     */
    JoyControl() : Node("joy_control")
    {
        this->declare_parameter("use_stamp_vel", true);
        this->declare_parameter("AXIS_FORWARD_BACKWARD", 5);
        this->declare_parameter("AXIS_LEFT_RIGHT", 4);
        this->declare_parameter("KEY_TURN_LEFT", 3);
        this->declare_parameter("KEY_TURN_RIGHT", 1);
        this->declare_parameter("KEY_SPEED_UP", 9);
        this->declare_parameter("KEY_STOP_PRESS", 7);
        this->declare_parameter("KEY_STOP_RELEASE", 5);

        this->declare_parameter("low_vel_x", 0.3);
        this->declare_parameter("high_vel_x", 0.5);
        this->declare_parameter("low_vel_y", 0.3);
        this->declare_parameter("high_vel_y", 0.5);
        this->declare_parameter("low_vel_yaw", 0.3);
        this->declare_parameter("high_vel_yaw", 0.8);
        this->declare_parameter("joy_timeout_ms", 500.0); // Joystick timeout in milliseconds

        this->get_parameter("AXIS_FORWARD_BACKWARD", AXIS_FORWARD_BACKWARD);
        this->get_parameter("AXIS_LEFT_RIGHT", AXIS_LEFT_RIGHT);
        this->get_parameter("KEY_TURN_LEFT", KEY_TURN_LEFT);
        this->get_parameter("KEY_TURN_RIGHT", KEY_TURN_RIGHT);
        this->get_parameter("KEY_SPEED_UP", KEY_SPEED_UP);
        this->get_parameter("KEY_STOP_PRESS", KEY_STOP_PRESS);
        this->get_parameter("KEY_STOP_RELEASE", KEY_STOP_RELEASE);

        this->get_parameter("low_vel_x", low_vel_x_);
        this->get_parameter("high_vel_x", high_vel_x_);
        this->get_parameter("low_vel_y", low_vel_y_);
        this->get_parameter("high_vel_y", high_vel_y_);
        this->get_parameter("low_vel_yaw", low_vel_yaw_);
        this->get_parameter("high_vel_yaw", high_vel_yaw_);
        this->get_parameter("use_stamp_vel", use_stamp_vel_);
        this->get_parameter("joy_timeout_ms", joy_timeout_ms_);

        if (use_stamp_vel_)
        {
            vel_stamp_pub_ = this->create_publisher<geometry_msgs::msg::TwistStamped>("/cmd_vel_joy", 1); // Publish chassis speed
        }
        else
        {
            vel_pub_ = this->create_publisher<geometry_msgs::msg::Twist>("/cmd_vel_joy", 1); // Publish chassis speed
        }
        cmd_pub_ = this->create_publisher<std_msgs::msg::String>("/motor_manager_cmd", 2); // Driver commands
        
        Nstop = 0;
        stop_counter_ = 0;
        
        // Initialize timestamp
        last_joy_time_ = this->now();
        is_joy_connected_ = false;

        // Create a timer to check joystick connection status
        timeout_timer_ = this->create_wall_timer(
            std::chrono::milliseconds(100), // Check every 100ms
            std::bind(&JoyControl::checkJoyTimeout, this));

        // Subscribe to joystick messages
        joy_sub_ = create_subscription<sensor_msgs::msg::Joy>("/joy", 1, std::bind(&JoyControl::joySubCallback, this, std::placeholders::_1));
    }

private:
    int AXIS_FORWARD_BACKWARD;
    int AXIS_LEFT_RIGHT;
    int KEY_TURN_LEFT;
    int KEY_TURN_RIGHT;
    int KEY_SPEED_UP;

    double low_vel_x_;
    double high_vel_x_;
    double low_vel_yaw_;
    double high_vel_yaw_;
    double low_vel_y_;
    double high_vel_y_;
    bool use_stamp_vel_;
    double joy_timeout_ms_; // Joystick timeout in milliseconds

    int Nstop;
    int stop_counter_;

    // Joystick connection status variables
    rclcpp::Time last_joy_time_; // Time of last received joystick message
    bool is_joy_connected_;      // Joystick connection status

    rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr vel_pub_;
    rclcpp::Publisher<geometry_msgs::msg::TwistStamped>::SharedPtr vel_stamp_pub_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr cmd_pub_;
    rclcpp::Subscription<sensor_msgs::msg::Joy>::SharedPtr joy_sub_;
    rclcpp::TimerBase::SharedPtr timeout_timer_; // Timeout check timer

    /**
     * @brief: Converts joystick message to chassis speed.
     * @param {SharedPtr} msg Joystick message
     * @param {Twist} &cmd_vel Chassis speed
     * @return {*}
     */
    void toBaseSpeed(const sensor_msgs::msg::Joy::SharedPtr msg, geometry_msgs::msg::Twist &cmd_vel)
    {
        bool speedup = false;
        if (msg->buttons[KEY_SPEED_UP] == 1)
            speedup = true; // High speed

        if (msg->buttons[KEY_TURN_LEFT] == 1)
        {
            if (speedup)
                cmd_vel.angular.z += high_vel_yaw_;
            else
                cmd_vel.angular.z += low_vel_yaw_;
        }
        if (msg->buttons[KEY_TURN_RIGHT] == 1)
        {
            if (speedup)
                cmd_vel.angular.z -= high_vel_yaw_;
            else
                cmd_vel.angular.z -= low_vel_yaw_;
        }

        int temp = judgeKey(msg->axes[AXIS_FORWARD_BACKWARD]);
        if (temp == 1)
        {
            if (speedup)
                cmd_vel.linear.x += high_vel_x_;
            else
                cmd_vel.linear.x += low_vel_x_;
        }
        else if (temp == -1)
        {
            if (speedup)
                cmd_vel.linear.x -= high_vel_x_;
            else
                cmd_vel.linear.x -= low_vel_x_;
        }

        temp = judgeKey(msg->axes[AXIS_LEFT_RIGHT]);
        if (temp == 1)
        {
            if (speedup)
                cmd_vel.linear.y += high_vel_y_;
            else
                cmd_vel.linear.y += low_vel_y_;
        }
        else if (temp == -1)
        {
            if (speedup)
                cmd_vel.linear.y -= high_vel_y_;
            else
                cmd_vel.linear.y -= low_vel_y_;
        }
    }

    /**
     * @brief: Publishes the velocity message.
     * @param {Twist} twist Velocity message
     * @return {*}
     */
    void publishVelCmd(const geometry_msgs::msg::Twist &twist)
    {
        if (use_stamp_vel_)
        {
            geometry_msgs::msg::TwistStamped cmd_vel;
            cmd_vel.header.stamp = this->now(); // Add current timestamp
            cmd_vel.twist = twist;
            vel_stamp_pub_->publish(cmd_vel);
        }
        else
        {
            vel_pub_->publish(twist);
        }
    }

    /**
     * @brief: Checks for joystick timeout and sends a stop command.
     * @return {*}
     */
    void checkJoyTimeout()
    {
        auto current_time = this->now();
        auto time_diff = (current_time - last_joy_time_).seconds() * 1000.0; // Convert to milliseconds

        if (time_diff > joy_timeout_ms_)
        {
            if (is_joy_connected_)
            {
                // Joystick just disconnected, send stop command
                RCLCPP_WARN(this->get_logger(), "Joy controller disconnected! Sending zero velocity command.");

                // Send stop velocity command
                geometry_msgs::msg::Twist stop_twist; // All velocities default to 0
                publishVelCmd(stop_twist);

                is_joy_connected_ = false;
            }
            else
            {
                if (stop_counter_ < 3)
                {
                    RCLCPP_INFO(this->get_logger(), "Resending zero velocity command (%d/3).", stop_counter_ + 1);
                    geometry_msgs::msg::Twist stop_twist;
                    publishVelCmd(stop_twist);
                    stop_counter_++;
                }
            }
        }
        else
        {
            if (!is_joy_connected_)
            {
                // Joystick reconnected
                RCLCPP_INFO(this->get_logger(), "Joy controller reconnected!");
                stop_counter_ = 0; // Reset counter on reconnection
                is_joy_connected_ = true;
            }
        }
    }

    /**
     * @brief: Joystick message callback function, processes joystick input to motion info.
     * @param {SharedPtr} msg Joystick message
     * @return {*}
     */
    void joySubCallback(const sensor_msgs::msg::Joy::SharedPtr msg)
    {
        // Update the time of the last received message
        last_joy_time_ = this->now();

        // If joystick just reconnected, mark as connected
        if (!is_joy_connected_)
        {
            RCLCPP_INFO(this->get_logger(), "Joy controller reconnected!");
            is_joy_connected_ = true;
        }
        
        std_msgs::msg::String cmd;
        // Emergency stop switch
        if (msg->buttons[KEY_STOP_RELEASE] == 1 || msg->buttons[KEY_STOP_PRESS] == 1)
        {
            cmd.data = "stop " + std::to_string(msg->buttons[KEY_STOP_PRESS]);
            cmd_pub_->publish(cmd);
            return;
        }

        // Chassis movement
        geometry_msgs::msg::Twist twist;
        toBaseSpeed(msg, twist);

        // Check if any movement buttons/axes are being pressed
        bool any_movement_input = (msg->buttons[KEY_TURN_LEFT] == 1) ||
                                 (msg->buttons[KEY_TURN_RIGHT] == 1) ||
                                 (abs(msg->axes[AXIS_FORWARD_BACKWARD]) > 0.01) ||
                                 (abs(msg->axes[AXIS_LEFT_RIGHT]) > 0.01);

        // Always publish if there's any movement input, or publish zero velocity a few times when stopping
        if (any_movement_input)
        {
            Nstop = 0;
            publishVelCmd(twist);
        }
        else
        {
            // No movement input - publish zero velocity a few times then stop
            if (Nstop < 5)
            {
                ++Nstop;
                publishVelCmd(twist);
            }
        }
    }

    /**
     * @brief: Normalizes velocity direction.
     * @param {double} &vel Velocity
     * @return {*}
     */
    int judgeKey(const double &vel)
    {
        // A small epsilon to account for float precision issues
        const double EPSILON = 0.01;
        if (vel >= EPSILON)
            return 1;
        else if (vel <= -EPSILON)
            return -1;
        else
            return 0;
    }
};

int main(int argc, char **argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<JoyControl>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}




// /**
//  * 游戏手柄控制底盘、升降、手抓、云台
//  */
// #include <rclcpp/rclcpp.hpp>
// #include <geometry_msgs/msg/twist.hpp>
// #include <geometry_msgs/msg/twist_stamped.hpp>
// #include <sensor_msgs/msg/joy.hpp>
// #include <std_msgs/msg/string.hpp>
// #include <chrono>

// class JoyControl : public rclcpp::Node
// {
// public:
//     /**
//      * @brief: 类构建函数，获取ROS参数，创建订阅者和发布者
//      * @return {*}
//      */
//     JoyControl() : Node("joy_control")
//     {
//         this->declare_parameter("use_stamp_vel", true);
//         this->declare_parameter("AXIS_FORWARD_BACKWARD", 5);
//         this->declare_parameter("AXIS_LEFT_RIGHT", 4);
//         this->declare_parameter("KEY_TURN_LEFT", 3);
//         this->declare_parameter("KEY_TURN_RIGHT", 1);
//         this->declare_parameter("KEY_SPEED_UP", 6);

//         this->declare_parameter("KEY_PANTILT_ON", 4);
//         this->declare_parameter("KEY_PAN_LEFT", 0);
//         this->declare_parameter("KEY_PAN_RIGHT", 2);
//         this->declare_parameter("KEY_TILT_UP", 3);
//         this->declare_parameter("KEY_TILT_DOWN", 1);
//         this->declare_parameter("AXIS_EV_UP_DOWN", 5);

//         this->declare_parameter("KEY_STOP_PRESS", 7);
//         this->declare_parameter("KEY_STOP_RELEASE", 5);
//         this->declare_parameter("KEY_PAW_OPEN", 8);
//         this->declare_parameter("KEY_PAW_CLOSE", 9);

//         this->declare_parameter("low_vel_x", 0.3);
//         this->declare_parameter("high_vel_x", 0.5);
//         this->declare_parameter("low_vel_y", 0.3);
//         this->declare_parameter("high_vel_y", 0.5);
//         this->declare_parameter("low_vel_yaw", 0.3);
//         this->declare_parameter("high_vel_yaw", 0.8);
//         this->declare_parameter("pantilt_increment", 5.0);
//         this->declare_parameter("ev_increment_", 0.03);
//         this->declare_parameter("joy_timeout_ms", 500.0);  // 手柄超时时间(毫秒)

//         this->get_parameter("AXIS_FORWARD_BACKWARD", AXIS_FORWARD_BACKWARD);
//         this->get_parameter("AXIS_LEFT_RIGHT", AXIS_LEFT_RIGHT);
//         this->get_parameter("KEY_TURN_LEFT", KEY_TURN_LEFT);
//         this->get_parameter("KEY_TURN_RIGHT", KEY_TURN_RIGHT);
//         this->get_parameter("KEY_SPEED_UP", KEY_SPEED_UP);

//         this->get_parameter("KEY_PANTILT_ON", KEY_PANTILT_ON);
//         this->get_parameter("KEY_PAN_LEFT", KEY_PAN_LEFT);
//         this->get_parameter("KEY_PAN_RIGHT", KEY_PAN_RIGHT);
//         this->get_parameter("KEY_TILT_UP", KEY_TILT_UP);
//         this->get_parameter("KEY_TILT_DOWN", KEY_TILT_DOWN);
//         this->get_parameter("AXIS_EV_UP_DOWN", AXIS_EV_UP_DOWN);

//         this->get_parameter("KEY_STOP_PRESS", KEY_STOP_PRESS);
//         this->get_parameter("KEY_STOP_RELEASE", KEY_STOP_RELEASE);
//         this->get_parameter("KEY_PAW_OPEN", KEY_PAW_OPEN);
//         this->get_parameter("KEY_PAW_CLOSE", KEY_PAW_CLOSE);

//         this->get_parameter("low_vel_x", low_vel_x_);
//         this->get_parameter("high_vel_x", high_vel_x_);
//         this->get_parameter("low_vel_y", low_vel_y_);
//         this->get_parameter("high_vel_y", high_vel_y_);
//         this->get_parameter("low_vel_yaw", low_vel_yaw_);
//         this->get_parameter("high_vel_yaw", high_vel_yaw_);
//         this->get_parameter("pantilt_increment", pantilt_increment_);
//         this->get_parameter("ev_increment_", ev_increment_);
//         this->get_parameter("use_stamp_vel", use_stamp_vel_);
//         this->get_parameter("joy_timeout_ms", joy_timeout_ms_);
//         if (use_stamp_vel_)
//         {
//             vel_stamp_pub_ = this->create_publisher<geometry_msgs::msg::TwistStamped>("/cmd_vel_joy", 1);       // 发布底盘速度
//         }
//         else
//         {
//             vel_pub_ = this->create_publisher<geometry_msgs::msg::Twist>("/cmd_vel_joy", 1);       // 发布底盘速度
//         }
//         cmd_pub_ = this->create_publisher<std_msgs::msg::String>("/motor_manager_cmd", 2); // 驱动命令
        
//         Nstop = 0;
//         stop_counter_ = 0;
        

//         // 初始化时间戳
//         last_joy_time_ = this->now();
//         is_joy_connected_ = false;

//         // 创建定时器检查手柄连接状态
//         timeout_timer_ = this->create_wall_timer(
//             std::chrono::milliseconds(100),  // 每100ms检查一次
//             std::bind(&JoyControl::checkJoyTimeout, this));

//         // 订阅手柄信息
//         joy_sub_ = create_subscription<sensor_msgs::msg::Joy>("/joy", 1, std::bind(&JoyControl::joySubCallback, this, std::placeholders::_1));
//     }

// private:
//     int AXIS_FORWARD_BACKWARD;
//     int AXIS_LEFT_RIGHT;
//     int KEY_TURN_LEFT;
//     int KEY_TURN_RIGHT;
//     int KEY_SPEED_UP;

//     int KEY_PANTILT_ON;
//     int KEY_PAN_LEFT;
//     int KEY_PAN_RIGHT;
//     int KEY_TILT_UP;
//     int KEY_TILT_DOWN;
//     int AXIS_EV_UP_DOWN;

//     int KEY_STOP_PRESS;
//     int KEY_STOP_RELEASE;
//     int KEY_PAW_OPEN;
//     int KEY_PAW_CLOSE;

//     double low_vel_x_;
//     double high_vel_x_;
//     double low_vel_yaw_;
//     double high_vel_yaw_;
//     double low_vel_y_;
//     double high_vel_y_;
//     double pantilt_increment_;
//     double ev_increment_;
//     bool   use_stamp_vel_;
//     double joy_timeout_ms_;  // 手柄超时时间(毫秒)

//     int Nstop;
//     int stop_counter_;

//     // 手柄连接状态相关变量
//     rclcpp::Time last_joy_time_;  // 最后收到手柄消息的时间
//     bool is_joy_connected_;       // 手柄连接状态

//     rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr vel_pub_;
//     rclcpp::Publisher<geometry_msgs::msg::TwistStamped>::SharedPtr vel_stamp_pub_;
//     rclcpp::Publisher<std_msgs::msg::String>::SharedPtr cmd_pub_;
//     rclcpp::Subscription<sensor_msgs::msg::Joy>::SharedPtr joy_sub_;
//     rclcpp::TimerBase::SharedPtr timeout_timer_;  // 超时检查定时器

//     /**
//      * @brief: 将手柄信息转换为底盘速度
//      * @param {SharedPtr} msg 手柄信息
//      * @param {Twist} &cmd_vel 底盘速度
//      * @return {*}
//      */
//     void toBaseSpeed(const sensor_msgs::msg::Joy::SharedPtr msg, geometry_msgs::msg::Twist &cmd_vel)
//     {
//         bool speedup = false;
//         if (msg->buttons[KEY_SPEED_UP] == 1)
//             speedup = true; // 高速度

//         if (msg->buttons[KEY_TURN_LEFT] == 1)
//         {
//             if (speedup)
//                 cmd_vel.angular.z += high_vel_yaw_;
//             else
//                 cmd_vel.angular.z += low_vel_yaw_;
//         }
//         if (msg->buttons[KEY_TURN_RIGHT] == 1)
//         {
//             if (speedup)
//                 cmd_vel.angular.z -= high_vel_yaw_;
//             else
//                 cmd_vel.angular.z -= low_vel_yaw_;
//         }

//         int temp = judgeKey(msg->axes[AXIS_FORWARD_BACKWARD]);
//         if (temp == 1)
//         {
//             if (speedup)
//                 cmd_vel.linear.x += high_vel_x_;
//             else
//                 cmd_vel.linear.x += low_vel_x_;
//         }
//         else if (temp == -1)
//         {
//             if (speedup)
//                 cmd_vel.linear.x -= high_vel_x_;
//             else
//                 cmd_vel.linear.x -= low_vel_x_;
//         }

//         temp = judgeKey(msg->axes[AXIS_LEFT_RIGHT]);
//         if (temp == 1)
//         {
//             if (speedup)
//                 cmd_vel.linear.y += high_vel_y_;
//             else
//                 cmd_vel.linear.y += low_vel_y_;
//         }
//         else if (temp == -1)
//         {
//             if (speedup)
//                 cmd_vel.linear.y -= high_vel_y_;
//             else
//                 cmd_vel.linear.y -= low_vel_y_;
//         }
//     }

//     /**
//      * @brief: 将手柄信息转换为tile/pan运动命令
//      * @param {SharedPtr} msg 手柄信息
//      * @param {String} &cmd 运动命令
//      * @return {*}
//      */
//     void toPanTiltCmd(const sensor_msgs::msg::Joy::SharedPtr msg, std_msgs::msg::String &cmd)

//     {
//         std::string incre = std::to_string(pantilt_increment_);

//         if (msg->buttons[KEY_PAN_LEFT] == 1)
//             cmd.data = "add pan " + incre;
//         else if (msg->buttons[KEY_PAN_RIGHT] == 1)
//             cmd.data = "add pan -" + incre;
//         if (msg->buttons[KEY_TILT_UP] == 1)
//             cmd.data = "add tilt " + incre;
//         else if (msg->buttons[KEY_TILT_DOWN] == 1)
//             cmd.data = "add tilt -" + incre;
//     }

//     /**
//      * @brief: 手柄信息转换为手抓运动
//      * @param {SharedPtr} msg 手柄信息
//      * @param {String} &cmd 运动命令
//      * @return {*}
//      */
//     void toPawCmd(const sensor_msgs::msg::Joy::SharedPtr msg, std_msgs::msg::String &cmd)
//     {
//         if (msg->buttons[KEY_PAW_OPEN] == 1)
//         {
//             cmd.data = "open paw";
//         }
//         else if (msg->buttons[KEY_PAW_CLOSE] == 1)
//         {
//             cmd.data = "close paw";
//         }
//     }

//     /**
//      * @brief: 发布速度消息
//      * @param {Twist} twist 速度信息
//      * @return {*}
//      */
//     void publishVelCmd(const geometry_msgs::msg::Twist &twist)
//     {
//         if (use_stamp_vel_)
//         {
//             geometry_msgs::msg::TwistStamped cmd_vel;
//             cmd_vel.header.stamp = this->now();  // 添加当前时间戳
//             cmd_vel.twist = twist;
//             vel_stamp_pub_->publish(cmd_vel);
//         }
//         else
//         {
//             vel_pub_->publish(twist);
//         }
//     }

//     /**
//      * @brief: 检查手柄超时并发送停止命令
//      * @return {*}
//      */
//     void checkJoyTimeout()
//     {
//         auto current_time = this->now();
//         auto time_diff = (current_time - last_joy_time_).seconds() * 1000.0;  // 转换为毫秒

//         if (time_diff > joy_timeout_ms_)
//         {
//             if (is_joy_connected_)
//             {
//                 // 手柄刚断开连接，发送停止命令
//                 RCLCPP_WARN(this->get_logger(), "Joy controller disconnected! Sending zero velocity command.");

//                 // 发送停止速度命令
//                 geometry_msgs::msg::Twist stop_twist;  // 默认所有速度为0
//                 publishVelCmd(stop_twist);

//                 is_joy_connected_ = false;
//             }
//             else
//             {
//                 if (stop_counter_ < 3)
//                 {
//                     RCLCPP_INFO(this->get_logger(), "Resending zero velocity command (%d/3).", stop_counter_ + 1);
//                     geometry_msgs::msg::Twist stop_twist;
//                     publishVelCmd(stop_twist);
//                     stop_counter_++;
//                 }
//             }
//         }
//         else
//         {
//             if (!is_joy_connected_)
//             {
//                 // 手柄重新连接
//                 RCLCPP_INFO(this->get_logger(), "Joy controller reconnected!");
//                 stop_counter_ = 0; // 重新连接时，将计数器清零
//                 is_joy_connected_ = true;
//             }
//         }
//     }

//     /**
//      * @brief: 手柄信息回调函数，内部处理手柄转运动信息
//      * @param {SharedPtr} msg 手柄信息
//      * @return {*}
//      */
//     void joySubCallback(const sensor_msgs::msg::Joy::SharedPtr msg)
//     {
//         // 更新最后收到消息的时间
//         last_joy_time_ = this->now();

//         // 如果手柄刚重新连接，标记为已连接
//         if (!is_joy_connected_)
//         {
//             RCLCPP_INFO(this->get_logger(), "Joy controller reconnected!");
//             is_joy_connected_ = true;
//         }
//         std_msgs::msg::String cmd;
//         // 急停开关
//         if (msg->buttons[KEY_STOP_RELEASE] == 1 || msg->buttons[KEY_STOP_PRESS] == 1)
//         {
//             cmd.data = "stop " + std::to_string(msg->buttons[KEY_STOP_PRESS]);
//             cmd_pub_->publish(cmd);
//             return;
//         }
//         // 手抓闭合
//         if (msg->buttons[KEY_PAW_OPEN] == 1 || msg->buttons[KEY_PAW_CLOSE] == 1)
//         {
//             if (msg->buttons[KEY_PAW_OPEN] == 1)
//                 cmd.data = "open paw";
//             else
//                 cmd.data = "close paw";
//             cmd_pub_->publish(cmd);
//         }
//         // 升降移动
//         else if (msg->axes[AXIS_EV_UP_DOWN] == 1 || msg->axes[AXIS_EV_UP_DOWN] == -1)
//         {
//             cmd.data = "add ev " + std::to_string(msg->axes[AXIS_EV_UP_DOWN] * ev_increment_);
//             cmd_pub_->publish(cmd);
//         }
//         // 云台移动
//         else if (msg->buttons[KEY_PANTILT_ON] == 1)
//         {
//             toPanTiltCmd(msg, cmd);
//             if (cmd.data != "")
//                 cmd_pub_->publish(cmd);
//         }
//         else // 底盘移动
//         {
//             geometry_msgs::msg::Twist twist;
//             toBaseSpeed(msg, twist);
//             if (twist.linear.x == 0 && twist.angular.z == 0)
//             {
//                 if (Nstop < 5)
//                 {
//                     ++Nstop;
//                     publishVelCmd(twist);
//                 }
//             }
//             else
//             {
//                 Nstop = 0;
//                 publishVelCmd(twist);
//             }
//         }
//     }

//     /**
//      * @brief: 速度方向化
//      * @param {double} &vel 速度
//      * @return {*}
//      */
//     int judgeKey(const double &vel)
//     {
//         if (vel >= 0.01)
//             return 1;
//         else if (vel <= -0.01)
//             return -1;
//         else
//             return 0;
//     }
// };

// int main(int argc, char **argv)
// {
//     rclcpp::init(argc, argv);
//     auto node = std::make_shared<JoyControl>();
//     rclcpp::spin(node);
//     rclcpp::shutdown();
//     return 0;
// }
